use std::io::{self, BufRead, Write};
use clap::{Parser, Subcommand};
use anyhow::Result;
use tracing::{info, debug};

async fn handle_chat_session() -> Result<()> {
    println!("REPL chat session started. Type `exit` to quit.");
    let stdin = io::stdin();
    for line in stdin.lock().lines() {
        let input = line?;
        if input.to_lowercase() == "exit" {
            break;
        }
        println!("You said: {}\n", input);
        print!("> ");
        io::stdout().flush()?;
    }
    println!("REPL chat session ended.");
    Ok(())
}


#[derive(Parser)]
#[command(name = "asher")]
#[command(about = "An AI agent CLI tool")]
#[command(version)]
struct Cli {
    #[command(subcommand)]
    command: Commands,
    
    /// Enable verbose logging
    #[arg(short, long)]
    verbose: bool,
}

#[derive(Subcommand)]
enum Commands {
    /// Start the agent
    Start {
        /// Configuration file path
        #[arg(short, long)]
        config: Option<String>,
    },
    /// Show status
    Status,
    /// Start a REPL chat session
    Chat,
}

#[tokio::main]
async fn main() -> Result<()> {
    let cli = Cli::parse();
    
    // Initialize logging
    let log_level = if cli.verbose { "debug" } else { "info" };
    tracing_subscriber::fmt()
        .with_env_filter(
            tracing_subscriber::EnvFilter::try_from_default_env()
                .unwrap_or_else(|_| tracing_subscriber::EnvFilter::new(log_level))
        )
        .init();

    info!("Starting Asher CLI");
    
    match cli.command {
        Commands::Start { config } => {
            debug!("Starting agent with config: {:?}", config);
            agent_core::start_agent(config).await?;
        }
        Commands::Status => {
            println!("Agent status: Ready");
        },
        Commands::Chat => {
            handle_chat_session().await?;
        }
    }
    
    Ok(())
} 