[package]
name = "agent-core"
version = "0.1.0"
edition = "2021"

[dependencies]
anyhow = "1.0"
async-stream = "0.3"
chrono = { version = "0.4", features = ["serde"] }
futures = "0.3"
reqwest = { version = "0.12.2", features = ["json", "stream"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
tokio = { version = "1.0", features = ["full"] }
tokio-util = { version = "0.7", features = ["codec"] }
url = "2.0"
tracing = "0.1"
tokio-stream = "0.1"

[dev-dependencies]
mockito = "1.4.0"
