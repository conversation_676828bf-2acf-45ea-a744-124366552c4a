use anyhow::Result;
use serde::{Deserialize, Serialize};
use std::path::Path;

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct Config {
    /// Agent name
    pub name: String,
    /// Maximum number of concurrent tasks
    pub max_concurrent_tasks: usize,
    /// Enable debug mode
    pub debug: bool,
    /// API configuration
    pub api: ApiConfig,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ApiConfig {
    /// API endpoint URL
    pub endpoint: String,
    /// API timeout in seconds
    pub timeout_seconds: u64,
    /// Maximum retries
    pub max_retries: u32,
}

impl Default for Config {
    fn default() -> Self {
        Self {
            name: "asher-agent".to_string(),
            max_concurrent_tasks: 10,
            debug: false,
            api: ApiConfig {
                endpoint: "http://localhost:8080".to_string(),
                timeout_seconds: 30,
                max_retries: 3,
            },
        }
    }
}

impl Config {
    /// Load configuration from a file
    pub fn from_file<P: AsRef<Path>>(path: P) -> Result<Self> {
        let content = std::fs::read_to_string(path)?;
        let config: Config = serde_json::from_str(&content)?;
        Ok(config)
    }
    
    /// Save configuration to a file
    pub fn to_file<P: AsRef<Path>>(&self, path: P) -> Result<()> {
        let content = serde_json::to_string_pretty(self)?;
        std::fs::write(path, content)?;
        Ok(())
    }
} 