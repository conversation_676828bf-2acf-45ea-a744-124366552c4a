use crate::providers::types::{
    Message, MessageStream, ModelOptions, ProviderConfig, ProviderUsage, Role, Usage, PromptTokensDetails
};
use anyhow::Result;
use async_stream::try_stream;
use chrono::Utc;
use futures::StreamExt;
use reqwest::header::HeaderMap;
use serde_json::json;
use tokio_util::codec::{FramedRead, LinesCodec};
use tokio_util::io::StreamReader;
use url::Url;

const DEFAULT_BASE_URL: &str = "https://openrouter.ai/api/v1/";
const DEFAULT_MODEL: &str = "google/gemini-2.5-flash-lite";

#[derive(Debug, Clone)]
pub struct OpenrouterProvider {
    config: ProviderConfig,
}

impl OpenrouterProvider {
    /// Create a new Openrouter provider with custom configuration
    pub fn new(config: ProviderConfig) -> Self {
        Self { config }
    }

    /// Create a new Openrouter provider with default settings and provided API key
    pub fn with_api_key(api_key: String) -> Self {
        Self {
            config: ProviderConfig {
                api_key,
                base_url: DEFAULT_BASE_URL.to_string(),
                default_model: DEFAULT_MODEL.to_string(),
            },
        }
    }

    /// Create a new provider with default settings (empty API key)
    pub fn default() -> Self {
        Self {
            config: ProviderConfig {
                api_key: String::new(),
                base_url: DEFAULT_BASE_URL.to_string(),
                default_model: DEFAULT_MODEL.to_string(),
            },
        }
    }

    /// Get the current configuration
    pub fn config(&self) -> &ProviderConfig {
        &self.config
    }

    /// update config
    pub fn set(&mut self, key: String, value: String) {
        match key.as_str() {
            "api_key" => self.config.api_key = value,
            "base_url" => self.config.base_url = value,
            "default_model" => self.config.default_model = value,
            _ => panic!("Invalid key: {}", key),
        }
    }

    pub async fn stream(
        &self,
        messages: Vec<Message>,
        options: Option<&ModelOptions>,
    ) -> Result<MessageStream> {
        let mut headers = HeaderMap::new();
        headers.insert(
            "Authorization",
            format!("Bearer {}", self.config.api_key).parse()?,
        );
        headers.insert("Content-Type", "application/json".parse()?);

        let mut payload = json!({
            "messages": messages,
        });

        println!("messages: {:?}", json!({
            "messages": messages,
        }));

        // Merge options into payload if provided
        if let Some(opts) = options {
            if let serde_json::Value::Object(opts_map) = serde_json::to_value(opts)? {
                if let serde_json::Value::Object(payload_map) = &mut payload {
                    for (key, value) in opts_map {
                        // Only add non-null values to avoid sending null fields to the API
                        if !value.is_null() {
                            payload_map.insert(key, value);
                        }
                    }
                }
            }
        }

        // Check if model is empty, if yes use default model
        if payload.get("model").is_none() {
            if let serde_json::Value::Object(payload_map) = &mut payload {
                payload_map.insert(
                    "model".to_string(),
                    serde_json::Value::String(self.config.default_model.clone()),
                );
            }
        }

        let url = Url::parse(&format!("{}/chat/completions", self.config.base_url))?;
        let client = reqwest::Client::new();
        let resp = client
            .post(url)
            .headers(headers)
            .json(&payload)
            .send()
            .await?;

        match resp.status() {
            reqwest::StatusCode::OK => {}
            _ => {
                println!("Failed to get response from model: {:?}", resp.status());
                return Err(anyhow::Error::msg("Failed to get response from model"));
            }
        }

        let stream = try_stream! {
            let byte_stream = resp.bytes_stream()
                .map(|result| result.map_err(|e| std::io::Error::new(std::io::ErrorKind::Other, e)));

            let stream_reader = StreamReader::new(byte_stream);
            let mut framed = FramedRead::new(stream_reader, LinesCodec::new());

            while let Some(line_result) = framed.next().await {
                let line = line_result.map_err(anyhow::Error::from)?;

                if line.starts_with("data: ") {
                    let json_str = line.trim_start_matches("data: ").trim();

                    // Check for end of stream marker
                    if json_str == "[DONE]" {
                        return;
                    }

                    if !json_str.is_empty() {
                        println!("raw json: {}", json_str);
                        let (message, usage) = Self::parse_chunk(json_str)?;
                        if message.is_some() || usage.is_some() {
                            yield (message, usage);
                        }
                    }
                }
            }
        };

        Ok(Box::pin(stream))
    }

    pub fn parse_chunk(json_str: &str) -> Result<(Option<Message>, Option<ProviderUsage>)> {
        match serde_json::from_str::<serde_json::Value>(json_str) {
            Ok(json) => {
                let mut message_result = None;
                let mut usage_result = None;

                // Handle streaming delta content
                if let Some(choices) = json.get("choices") {
                    if let Some(choice) = choices.get(0) {
                        if let Some(delta) = choice.get("delta") {
                            let role = delta
                                .get("role")
                                .and_then(|r| r.as_str())
                                .unwrap_or("assistant");
                            let content =
                                delta.get("content").and_then(|c| c.as_str()).unwrap_or("");

                            // Only create message if there's actual content
                            if !content.is_empty() || !role.is_empty() {
                                message_result = Some(Message {
                                    role: match role {
                                        "assistant" => Role::Assistant,
                                        "user" => Role::User,
                                        "system" => Role::System,
                                        "tool" => Role::Tool,
                                        _ => Role::User,
                                    },
                                    content: content.to_string(),
                                });
                            }
                        }
                    }
                }

                // Handle usage information
                if let Some(usage) = json.get("usage") {
                    if let (Some(prompt_tokens), Some(completion_tokens), Some(total_tokens)) = (
                        usage.get("prompt_tokens").and_then(|v| v.as_u64()),
                        usage.get("completion_tokens").and_then(|v| v.as_u64()),
                        usage.get("total_tokens").and_then(|v| v.as_u64()),
                    ) {
                        if let (Some(model), Some(provider)) = (
                            json.get("model").and_then(|m| m.as_str()),
                            json.get("provider").and_then(|p| p.as_str()),
                        ) {
                            if let Some(prompt_tokens_details) = usage.get("prompt_tokens_details") {
                                if let Some(cached_tokens) = prompt_tokens_details.get("cached_tokens").and_then(|v| v.as_u64()) {
                                    usage_result = Some(ProviderUsage {
                                        model: model.to_string(),
                                        provider: provider.to_string(),
                                        timestamp: Utc::now(),
                                        usage: Usage {
                                            prompt_tokens: prompt_tokens as u32,
                                            completion_tokens: completion_tokens as u32,
                                            total_tokens: total_tokens as u32,
                                            prompt_tokens_details: PromptTokensDetails {
                                                cached_tokens: cached_tokens as u32,
                                            },
                                        },
                                    });
                                }
                            }
                        }
                    }
                }

                // Return if we found meaningful data
                if message_result.is_some() || usage_result.is_some() {
                    return Ok((message_result, usage_result));
                }
                Ok((None, None))
            }
            Err(e) => {
                println!("Failed to parse json: {:?}", e);
                Err(anyhow::Error::from(e))
            }
        }
    }
}