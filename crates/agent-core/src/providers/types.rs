use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use tokio_stream::Stream;
use std::pin::Pin;
use anyhow::Result;

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct ProviderConfig {
    pub api_key: String,
    pub base_url: String,
    pub default_model: String,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, Default, Copy)]
pub struct PromptTokensDetails {
    pub cached_tokens: u32,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, Copy, Default)]
pub struct Usage {
    pub prompt_tokens: u32,
    pub completion_tokens: u32,
    pub total_tokens: u32,
    pub prompt_tokens_details: PromptTokensDetails,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize, Default)]
pub struct ProviderUsage {
    pub usage: Usage,
    pub model: String,
    pub provider: String,
    pub timestamp: DateTime<Utc>,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, Default)]
#[serde(rename_all = "snake_case")]
pub enum Role {
    System,
    #[default]
    User,
    Assistant,
    Tool,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, Default)]
#[serde(rename_all = "snake_case")]
pub struct Message {
    pub role: Role,
    pub content: String,
}

pub type MessageStream = Pin<Box<dyn Stream<Item = Result<(Option<Message>, Option<ProviderUsage>)>> + Send>,>;

#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct IncludeUsage {
    pub include: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct ModelOptions {
    pub model: Option<String>,
    pub temperature: Option<f32>,
    pub max_tokens: Option<u32>,
    pub top_p: Option<f32>,
    pub frequency_penalty: Option<f32>,
    pub presence_penalty: Option<f32>,
    pub stop: Option<String>,
    pub stream: Option<bool>,
    pub usage: IncludeUsage,
}

impl ModelOptions {
    pub fn with_defaults() -> Self {
        Self {
            model: None, // Will use provider's default model
            temperature: Some(0.7),
            max_tokens: Some(4096),
            top_p: Some(1.0),
            frequency_penalty: Some(0.0),
            presence_penalty: Some(0.0),
            stop: None,
            stream: Some(true),
            usage: IncludeUsage { include: true }, // NOTE: usage info may be only displayed in the last message
        }
    }
}