use anyhow::Result;
use tracing::{info, warn};

pub mod config;
pub mod engine;
pub mod providers;

pub use config::Config;
pub use engine::AgentEngine;

/// Start the agent with optional configuration
pub async fn start_agent(config_path: Option<String>) -> Result<()> {
    info!("Initializing agent core...");
    
    let config = match config_path {
        Some(path) => {
            info!("Loading configuration from: {}", path);
            Config::from_file(&path)?
        }
        None => {
            warn!("No configuration file provided, using defaults");
            Config::default()
        }
    };
    
    let engine = AgentEngine::new(config)?;
    engine.run().await?;
    
    Ok(())
} 