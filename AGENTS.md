# Agent Guidelines for Asher Project

This document outlines the conventions and commands for agentic coding in the Asher project.

## 1. Build, Lint, and Test Commands

- **Build:** `cargo build`
- **Run tests:** `cargo test`
- **Run a single test:** `cargo test <test_name>` (e.g., `cargo test my_function_test`)
- **Check code style/lint:** `cargo clippy`
- **Format code:** `cargo fmt`

## 2. Code Style Guidelines

- **Imports:** Organize `use` statements alphabetically and group them (e.g., standard library, external crates, internal modules).
- **Formatting:** Adhere to `rustfmt` defaults. Run `cargo fmt` before committing.
- **Types:** Use explicit types where clarity is improved; otherwise, leverage Rust's type inference.
- **Naming Conventions:**
    - `snake_case` for functions, variables, and modules.
    - `PascalCase` for types (structs, enums, traits).
    - `SCREAMING_SNAKE_CASE` for constants.
- **Error Handling:** Prefer `Result` and `Option` for recoverable errors. Use `panic!` sparingly for unrecoverable errors or during prototyping.
- **Comments:** Add comments for complex logic or non-obvious decisions. Explain *why*, not *what*.
- **Modularity:** Keep functions and modules small and focused on a single responsibility.
