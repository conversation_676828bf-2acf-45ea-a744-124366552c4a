# Asher

An AI agent built with Rust.

## Project Structure

This is a Cargo workspace with the following crates:

- **`crates/cli`** - Command-line interface (entry point)
- **`crates/agent_core`** - Core agent functionality

## Getting Started

### Prerequisites

- Rust (latest stable version)
- Cargo (comes with Rust)

### Building

```bash
# Build all crates
cargo build

# Build in release mode
cargo build --release
```

### Running

```bash
# Run the CLI
cargo run --bin asher -- --help

# Start the agent
cargo run --bin asher -- start

# Check agent status
cargo run --bin asher -- status

# Run with verbose logging
cargo run --bin asher -- --verbose start
```

### Development

```bash
# Check code without building
cargo check

# Run tests
cargo test

# Format code
cargo fmt

# Run clippy lints
cargo clippy
```

## Configuration

The agent can be configured using a JSON configuration file:

```bash
cargo run --bin asher -- start --config config.json
```

Example configuration:

```json
{
  "name": "my-agent",
  "max_concurrent_tasks": 10,
  "debug": true,
  "api": {
    "endpoint": "http://localhost:8080",
    "timeout_seconds": 30,
    "max_retries": 3
  }
}
```

## Architecture

- **CLI Crate**: Handles command-line parsing and user interaction
- **Agent Core Crate**: Contains the main agent logic, configuration, and engine
- **Modular Design**: Easy to extend with additional crates for specific functionality

## License

MIT OR Apache-2.0